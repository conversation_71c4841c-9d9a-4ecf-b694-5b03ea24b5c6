package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;

/**
 * Rworker自动设备接口
 */
@FuncDefault
public interface IRworkerDeviceService {

    /**
     * 执行下一个自动工序
     */
    default void executeNextAutoStep(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, boolean subWsProductionMode) {
    }
}
