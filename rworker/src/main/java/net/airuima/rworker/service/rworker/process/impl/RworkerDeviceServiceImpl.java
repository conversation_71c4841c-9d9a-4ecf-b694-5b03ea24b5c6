package net.airuima.rworker.service.rworker.process.impl;

import com.alibaba.fastjson.JSON;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rworker.domain.RworkerDeviceCache;
import net.airuima.rworker.repository.RworkerDeviceCacheRepository;
import net.airuima.rworker.service.rworker.process.IRworkerDeviceService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Rworker自动设备接口
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class RworkerDeviceServiceImpl implements IRworkerDeviceService {

    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private RworkerDeviceCacheRepository rworkerDeviceCacheRepository;

    @Override
    public void executeNextAutoStep(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, boolean subWsProductionMode) {
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId()) : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId());
        List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(rworkerBatchStepSaveRequestDTO.getStepId())).findFirst().orElseThrow(() -> new ResponseException("error.wsStepNotExist", "工单工序快照不存在"));
        String afterStepId = currWsStep.getAfterStepId();
        if (StringUtils.isNotBlank(afterStepId)) {
            //过滤下一个采用设备报工方式的工序
            List<WsStep> afterStepList = wsStepList.stream().filter(wsStep -> afterStepId.contains(wsStep.getStep().getId().toString()) && wsStep.getRequestMethod() == Constants.INT_TWO).toList();
            if (!afterStepList.isEmpty()) {
                for (WsStep wsStep : afterStepList) {
                    RworkerDeviceCache rworkerDeviceCache = new RworkerDeviceCache();
                    rworkerDeviceCache.setWorkSheet(workSheet);
                    rworkerDeviceCache.setSubWorkSheet(subWorkSheet);
                    rworkerDeviceCache.setStep(wsStep.getStep());
                    rworkerDeviceCache.setStatus(Constants.INT_ZERO);
                    rworkerDeviceCache.setNumber(Constants.INT_ZERO);
                    rworkerDeviceCache.setType(subWsProductionMode ? Constants.INT_ONE : Constants.INT_ZERO);
                    RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequest = new RworkerBatchStepSaveRequestDTO();
                    rworkerBatchStepSaveRequest.setProductWorkSheetId(subWsProductionMode ? subWorkSheet.getId() : workSheet.getId());
                    rworkerBatchStepSaveRequest.setStepId(wsStep.getStep().getId());
                    rworkerBatchStepSaveRequest.setNumber(subWsProductionMode ? subWorkSheet.getNumber() : workSheet.getNumber());
                    rworkerDeviceCache.setCache(JSON.toJSONString(rworkerBatchStepSaveRequest));
                    rworkerDeviceCacheRepository.save(rworkerDeviceCache);

                }
            }
        }
    }
}
