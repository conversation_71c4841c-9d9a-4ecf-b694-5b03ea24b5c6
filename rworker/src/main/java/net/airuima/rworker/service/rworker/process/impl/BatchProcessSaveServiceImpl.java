package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.domain.base.AuditSfIdEntity;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendHistory;
import net.airuima.rbase.domain.procedure.quality.WorkCellExtendStepDetail;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.ocmes.BakeCycleBakeAgeingSaveRequestDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.organization.TeamDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.organization.RbaseTeamProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendHistoryRepository;
import net.airuima.rbase.repository.procedure.quality.WorkCellExtendStepDetailRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.scene.NextTodoStepRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.rbase.service.procedure.aps.plugin.ISaleOrderService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rbase.proxy.downgrade.RbaseDownGradeProxy;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.device.IRworkerDeviceService;
import net.airuima.rworker.service.rworker.dynamic.IDynamicService;
import net.airuima.rworker.service.rworker.facility.IFacilityService;
import net.airuima.rworker.service.rworker.facility.IWearingPartService;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.rworker.service.rworker.oem.IOemService;
import net.airuima.rworker.service.rworker.process.*;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerStepProcessGlobalDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 工单模式工序保存相关Service
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class BatchProcessSaveServiceImpl implements IBatchProcessSaveService {

    @Autowired
    private CommonService commonService;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private IBatchProcessRequestService[] batchProcessRequestServices;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IProductionPlanService[] productionPlanServices;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private IWorkSheetApsService[] workSheetApsServices;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private IOemService[] oemServices;
    @Autowired
    private IParallelStepProcessService[] parallelStepProcessServices;
    @Autowired
    private BatchWorkDetailService batchWorkDetailService;
    @Autowired
    private RbaseTeamProxy rbaseTeamProxy;
    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private NextTodoStepRepository nextTodoStepRepository;
    @Autowired
    private ISaleOrderService[] iSaleOrderServices;
    @Autowired
    private RbaseDownGradeProxy rbaseDownGradeProxy;
    @Autowired
    private WorkCellExtendHistoryRepository workCellExtendHistoryRepository;
    @Autowired
    private WorkCellExtendStepDetailRepository workCellExtendStepDetailRepository;
    @Autowired
    private IRworkerDeviceService[] rworkerDeviceServices;

    /**
     * 保存批量模式下生产工序信息
     *
     * @param rworkerBatchStepSaveRequestDTOList 保存批量模式工序生产信息DTO列表
     */
    public void createStep(List<RworkerBatchStepSaveRequestDTO> rworkerBatchStepSaveRequestDTOList) {
        Set<RworkerBatchStepSaveRequestDTO> rworkerBatchStepSaveRequestDTOSet = new HashSet<>();
        Map<String, List<RworkerBatchStepSaveRequestDTO>> rworkerBatchStepSaveRequestDTOGroup = rworkerBatchStepSaveRequestDTOList.stream().collect(Collectors.groupingBy(rworkerBatchStepSaveRequestDTO -> rworkerBatchStepSaveRequestDTO.getStepId() + Constants.UNDERLINE + rworkerBatchStepSaveRequestDTO.getProductWorkSheetId()));
        rworkerBatchStepSaveRequestDTOGroup.forEach((key, rworkerBatchStepSaveRequestDtos) -> rworkerBatchStepSaveRequestDTOSet.add(rworkerBatchStepSaveRequestDtos.get(Constants.INT_ZERO)));
        rworkerBatchStepSaveRequestDTOList = new ArrayList<>(rworkerBatchStepSaveRequestDTOSet);
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        //获取生产缓存中的工序开始时间
        RworkerCache rworkerCache = rworkerCacheServices[0].findCacheByWorkCell(rworkerBatchStepSaveRequestDTOList.get(Constants.INT_ZERO).getWorkCellId());
        LocalDateTime startTime = Objects.nonNull(rworkerCache) ? rworkerCache.getStartTime() : rworkerBatchStepSaveRequestDTOList.get(Constants.INT_ZERO).getStartTime();
        RworkerBatchStepSaveRequestDTO stepSaveRequestDTO = rworkerBatchStepSaveRequestDTOList.get(Constants.INT_ZERO);
        WorkCell workCell = workCellRepository.getReferenceById(stepSaveRequestDTO.getWorkCellId());
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(stepSaveRequestDTO.getStaffId(), Constants.LONG_ZERO);
        TeamDTO teamDTO = Optional.ofNullable(stepSaveRequestDTO.getTeamId()).map(id -> rbaseTeamProxy.findByIdAndDeleted(id, Constants.LONG_ZERO)).orElse(null);
        RworkerStepProcessGlobalDTO rworkerStepProcessGlobalDTO = new RworkerStepProcessGlobalDTO();
        if (rworkerBatchStepSaveRequestDTOList.stream().anyMatch(rworkerBatchStepSaveRequestDTO -> rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber() > Constants.INT_ZERO && rworkerBatchStepSaveRequestDTO.getReInspect())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.STEP_REINSPECT_SERIAL_NUMBER, null, null);
            rworkerStepProcessGlobalDTO.setStepReinspectSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
        }
        //保存可能的工序易损件规则对应的最新使用易损件信息
        RworkerBatchStepSaveRequestDTO lastRworkerBatchStepSaveRequestDTO = rworkerBatchStepSaveRequestDTOList.get(rworkerBatchStepSaveRequestDTOList.size() - Constants.INT_ONE);
        if (!CollectionUtils.isEmpty(lastRworkerBatchStepSaveRequestDTO.getWearingPartInfoList())) {
            wearingPartServices[0].saveLatestStepWearingPart(lastRworkerBatchStepSaveRequestDTO.getWearingPartInfoList());
        }
        rworkerBatchStepSaveRequestDTOList.forEach(rworkerBatchStepSaveRequestDTO -> {
            rworkerDeviceServices[0].batchCheck(subWsProductionMode, rworkerBatchStepSaveRequestDTO);
            rworkerBatchStepSaveRequestDTO.setStartTime(startTime);
            this.createSingleBatchStep(rworkerBatchStepSaveRequestDTO, rworkerStepProcessGlobalDTO, workCell, staffDTO, teamDTO, subWsProductionMode);
            rworkerDeviceServices[0].finish(rworkerBatchStepSaveRequestDTO);
        });
        //删除可能的生产缓存信息
        rworkerCacheServices[0].deletedCacheByWorkCell(rworkerBatchStepSaveRequestDTOList.get(Constants.INT_ZERO).getWorkCellId());
    }

    /**
     * 保存单个批量工序生产数据
     *
     * @param rworkerBatchStepSaveRequestDTO 保存单个批量工序生产数据参数
     * @param subWsProductionMode            true:子工单粒度，false:工单粒度
     */
    @Klock(keys = {"#rworkerBatchStepSaveRequestDTO.producrWorkSheetId", "#rworkerBatchStepSaveRequestDTO.stepId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    private void createSingleBatchStep(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessGlobalDTO rworkerStepProcessGlobalDTO, WorkCell workCell, StaffDTO staffDTO, TeamDTO teamDTO, boolean subWsProductionMode) {
        BatchWorkDetail batchWorkDetail;
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId()) : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId());
        List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(rworkerBatchStepSaveRequestDTO.getStepId())).findFirst().orElseThrow(() -> new ResponseException("error.wsStepNotExist", "工单工序快照不存在"));
        Step step = currWsStep.getStep();
        //若前置工序存在，增加SN参数不可为空验证
        if (StringUtils.isNotBlank(currWsStep.getPreStepId())) {
            for (String preStepId : currWsStep.getPreStepId().split(Constants.STR_COMMA)) {
                SnWorkDetail snWorkDetail = subWsProductionMode ? snWorkDetailRepository.findTop1BySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), Long.parseLong(preStepId), net.airuima.constant.Constants.LONG_ZERO).orElse(null)
                        : snWorkDetailRepository.findTop1ByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), Long.parseLong(preStepId), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
                if (Objects.nonNull(snWorkDetail)) {
                    throw new ResponseException("error.currentStepMustHaveSn", "前置工序存在单支SN，当前工序待保存参数中无投产SN参数");
                }
            }
        }
        RworkerStepProcessBaseDTO batchStepSaveBaseInfo = new RworkerStepProcessBaseDTO(subWsProductionMode, workSheet, subWorkSheet, workCell, currWsStep, step, staffDTO, teamDTO, wsStepList);
        batchStepSaveBaseInfo.setWorkFlow(workSheet.getWorkFlow());
        //验证工序时间间隔是否合规
        BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateBatchStepIntervalWhenSave(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo);
        if (Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException(baseResultDTO.getKey(), baseResultDTO.getMessage());
        }
        if ((rworkerBatchStepSaveRequestDTO.getQualifiedNumber() + rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber()) > rworkerBatchStepSaveRequestDTO.getNumber()) {
            throw new ResponseException("error.numberIllegal", "参数异常:合格数+不合格数超过投产数");
        }
        batchStepSaveBaseInfo.setStepReinspectSerialNumber(rworkerStepProcessGlobalDTO.getStepReinspectSerialNumber());
        //过滤掉不良数量为空的不良项目数据
        if (!CollectionUtils.isEmpty(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            rworkerBatchStepSaveRequestDTO.setUnqualifiedItemInfoList(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().filter(unqualifiedItemSaveInfo -> Objects.nonNull(unqualifiedItemSaveInfo.getNumber())).toList());
        }
        //投产粒度为子工单时则保存子工单生产详情数据
        if (subWsProductionMode) {
            batchWorkDetail = this.createSingleSubWsBatchStep(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo);
        } else {
            //投产粒度为工单时则保存工单生产详情数据
            batchWorkDetail = this.createSingleWsBatchStep(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo);
        }
        //保存批量详情关联的追溯数据(物料批次、设备不良、易损件等)
        this.updateBatchWorkDetailRelationInfo(batchWorkDetail, rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo);

        //(子)工单是不存在处于维修分析状态且当前工序完成、流转数为0且没有在线调整工序时则需要完成（子）工单
        if (!batchStepSaveBaseInfo.getInspectTask() && StringUtils.isNotBlank(batchStepSaveBaseInfo.getWsStep().getAfterStepId())
                && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()
                && batchWorkDetail.getTransferNumber() == Constants.INT_ZERO
                && currWsStep.getCategory() != StepCategoryEnum.ONLINE_ADJUSTMENT_STEP.getStatus() && currWsStep.getCategory() != StepCategoryEnum.ONLINE_PRELIMINARY_ADJUSTMENT_STEP.getStatus()) {
            List<WsStep> childWsStepList = new ArrayList<>();
            commonService.findChildWsStep(batchStepSaveBaseInfo.getWsStepList(), childWsStepList, batchStepSaveBaseInfo.getWsStep());
            if (!CollectionUtils.isEmpty(childWsStepList)) {
                List<WsStep> onlineAdjustWsSteoList = childWsStepList.stream().filter(wsStep -> wsStep.getCategory() == StepCategoryEnum.ONLINE_ADJUSTMENT_STEP.getStatus() || wsStep.getCategory() == StepCategoryEnum.ONLINE_PRELIMINARY_ADJUSTMENT_STEP.getStatus()).toList();
                if (CollectionUtils.isEmpty(onlineAdjustWsSteoList)) {
                    BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).calculateBatchAfterAllStepFinished(batchStepSaveBaseInfo, batchWorkDetail.getTransferNumber());
                }
            }
        }
        //如果是工单请求时需要解绑该工单的所有容器
        List<WsStep> parentWsStepList = new ArrayList<>();
        commonService.findParentWsStep(wsStepList, parentWsStepList, currWsStep);
        this.unBindContainerWhenWsRequest(batchStepSaveBaseInfo, parentWsStepList);
        //更新生产计划表 工序组
        updateStepGroupProductionPlan(rworkerBatchStepSaveRequestDTO, workSheet, step);
        //保存烘烤温循老化记录
        if (null != batchWorkDetail && null != batchWorkDetail.getId() && currWsStep.getCategory() >= StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() && batchWorkDetail.getStep().getCategory() <= StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDTO = new BakeCycleBakeAgeingSaveRequestDTO(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo);
            //如果当前工序是取出的话，则找到上面最近一个放入工序
            if (currWsStep.getCategory() % net.airuima.constant.Constants.INT_TWO == net.airuima.constant.Constants.INT_ZERO) {
                if (StringUtils.isBlank(batchStepSaveBaseInfo.getWsStep().getPreStepId())) {
                    throw new ResponseException("error.BatchWorkDetailNull", "未检测放入工序记录");
                }
                WsStep latestWsStep = batchStepSaveBaseInfo.getWsStepList().stream().filter(wsStep1 -> batchStepSaveBaseInfo.getWsStep().getPreStepId().contains(String.valueOf(wsStep1.getStep().getId())) && wsStep1.getCategory() == batchStepSaveBaseInfo.getWsStep().getCategory() - 1).findFirst().orElse(null);
                if (Objects.isNull(latestWsStep)) {
                    throw new ResponseException("error.BatchWorkDetailNull", "未检测放入工序记录");
                }
                bakeCycleBakeAgeingSaveRequestDTO.setPutInStepId(latestWsStep.getStep().getId());
            }
            bakeCycleBakeAgeingModelServices[0].saveBakeCycleBakeAgeingHistoryInfo(bakeCycleBakeAgeingSaveRequestDTO);
        }
        //创建工序外协
        oemServices[0].grenadeStepOemOrder(batchWorkDetail);
        //完工上传
        workSheetApsServices[0].wsCompleteUpload(currWsStep, subWorkSheet, workSheet);
    }

    /**
     * 更新生产计划表 工序组
     *
     * @param rworkerBatchStepSaveRequestDTO 批量工序参数
     * @param workSheet                      工单
     * @param step                           工序
     */
    private void updateStepGroupProductionPlan(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, WorkSheet workSheet, Step step) {
        // 产品谱系id
        Long pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
        // 工序组
        Long stepGroupId = Optional.ofNullable(step).map(Step::getStepGroup).map(AuditSfIdEntity::getId).orElse(null);
        Long workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)) {
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId, rworkerBatchStepSaveRequestDTO.getQualifiedNumber(), OperationEnum.ADD);
        }
    }

    /**
     * 保存子工单粒度投产的批量工序生产数据
     *
     * @param rworkerBatchStepSaveRequestDTO 请求保存参数
     */
    private BatchWorkDetail createSingleSubWsBatchStep(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        SubWorkSheet subWorkSheet = batchStepSaveBaseInfo.getSubWorkSheet();
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(rworkerBatchStepSaveRequestDTO.getStepId(), rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            throw new ResponseException("error.stepIsFinished", "工序已完成");
        }
        //获取前置工序总共待流转数量，需要区分在线调整工序和普通工序
        //前置工序待流转数量
        int preStepTransferNumber;
        //前置工序合格数量
        int preStepQualifiedNumber = this.preStepTransferNumber(batchStepSaveBaseInfo, rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), Boolean.TRUE);
        //是否为在线调整类型工序
        boolean onlineReworkStep = batchStepSaveBaseInfo.getWsStep().getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || batchStepSaveBaseInfo.getWsStep().getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName();
        if (onlineReworkStep) {
            preStepTransferNumber = batchProcessRequestServices[0].findOnlineReworkStepTransferNumber(batchStepSaveBaseInfo.getSubWorkSheet().getId(), rworkerBatchStepSaveRequestDTO.getStepId(), Boolean.TRUE, batchStepSaveBaseInfo);
        } else {
            preStepTransferNumber = preStepQualifiedNumber;
        }
        batchStepSaveBaseInfo.setRealPreStepTransferNumber(preStepQualifiedNumber);
        preStepTransferNumber = (int) Math.ceil(NumberUtils.multiply(preStepTransferNumber, batchStepSaveBaseInfo.getWsStep().getInputRate()).doubleValue());
        //验证请求保存参数
        BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).validateInputNumber(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, batchWorkDetail, preStepTransferNumber);
        //记录子工单和工单的实际开工时间以及更改工单状态为投产中
        if (null == subWorkSheet.getActualStartDate()) {
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
        }
        if (null == workSheet.getActualStartDate()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
        }
        //保存子工单工序详情数据
        BatchWorkDetail saveBatchWorkDetail = BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).saveBatchWorkDetail(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, batchWorkDetail, preStepTransferNumber, preStepQualifiedNumber,onlineReworkStep);
        // 复检-》 维修分析-》 待检任务（抽检，终检，末检）-》 待做工序 （传递关系中 任何一个节点为true 后续节点不执行）
        //保存可能需要复检的不良数据
        boolean stepReinspect = StringUtils.isNotBlank(batchStepSaveBaseInfo.getStepReinspectSerialNumber()) ? qualityServices[0].saveBatchProcessReinspectInfo(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, saveBatchWorkDetail):Boolean.FALSE;
        //没有复检时保存可能存在的维修分析数据
        if(!stepReinspect) {
            stepReinspect = qualityServices[0].saveBatchProcessMaintainInfo(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, saveBatchWorkDetail);
        }
        //如果生成了维修分析记录，则不生成抽检终检任务
        if (!stepReinspect) {
            //是否需要进行抽检,终检
            boolean inspectTask = qualityServices[0].createBatchInspectTask(Boolean.TRUE,subWorkSheet,null,saveBatchWorkDetail.getStep(),saveBatchWorkDetail.getQualifiedNumber());
            batchStepSaveBaseInfo.setInspectTask(inspectTask);
        }
        //存在复检 类似与存在待检任务
        if(stepReinspect && !batchStepSaveBaseInfo.getInspectTask()){
            batchStepSaveBaseInfo.setInspectTask(stepReinspect);
        }
        if (!batchStepSaveBaseInfo.getInspectTask()) {
            //更新工作台下个可能待做的工序信息
            nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(Objects.nonNull(subWorkSheet) ? subWorkSheet.getId() : workSheet.getId(), workSheet, subWorkSheet,
                    batchStepSaveBaseInfo.getWsStepList(), saveBatchWorkDetail, Boolean.FALSE);
        }
        //最后一个工序完成后需要更新工单及销售订单相关数据
        if (StringUtils.isBlank(batchStepSaveBaseInfo.getWsStep().getAfterStepId()) && saveBatchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).calculateBatchAfterAllStepFinished(batchStepSaveBaseInfo, saveBatchWorkDetail.getTransferNumber());
        }
        //最后一个工序
        if (StringUtils.isBlank(batchStepSaveBaseInfo.getWsStep().getAfterStepId())) {
            //更新生产计划表 生产线 和工单统计
            updateProductionPlanAndWorkSheetStatistics(rworkerBatchStepSaveRequestDTO, workSheet, saveBatchWorkDetail);
        }
        return saveBatchWorkDetail;
    }

    /**
     * 更新生产计划表和工单统计表
     *
     * @param rworkerBatchStepSaveRequestDTO 保存批量工序参数
     * @param workSheet                      工单
     * @param saveBatchWorkDetail            批量工序生产详情
     */
    private void updateProductionPlanAndWorkSheetStatistics(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, WorkSheet workSheet, BatchWorkDetail saveBatchWorkDetail) {
        Long pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
        Long workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)) {
            productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, rworkerBatchStepSaveRequestDTO.getQualifiedNumber(), OperationEnum.ADD);
        }
        // 更新工单统计
        if (Objects.nonNull(workSheet)) {
            workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), rworkerBatchStepSaveRequestDTO.getQualifiedNumber(), saveBatchWorkDetail.getUnqualifiedNumber(), OperationEnum.ADD);
        }
    }

    /**
     * 验证保存批量工序参数参数
     *
     * @param rworkerBatchStepSaveRequestDTO 请求保存参数
     * @param batchStepSaveBaseInfo          工序生产过程基础信息
     * @param batchWorkDetail                批量工序生产详情
     * @param preStepTransferNumber          前置流转数
     * @return : void
     * <AUTHOR>
     * @date 2023/2/15
     **/
    @Override
    public void validateInputNumber(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, BatchWorkDetail batchWorkDetail, Integer preStepTransferNumber) {
        if (batchWorkDetail.getInputNumber() + rworkerBatchStepSaveRequestDTO.getNumber() > preStepTransferNumber) {
            throw new ResponseException("error.stepInputNumberOverPreStep", "工序投入数已超过前置工序流转数量");
        }
        //子工单/工单投产数
        int number = Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ? batchStepSaveBaseInfo.getSubWorkSheet().getNumber() : batchStepSaveBaseInfo.getWorkSheet().getNumber();
        if (batchWorkDetail.getInputNumber() + rworkerBatchStepSaveRequestDTO.getNumber() > number) {
            throw new ResponseException("error.stepInputNumberOverWorkSheet", Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ? "工序投入数已超过子工单数量" : "工序投入数已超过工单数量");
        }
    }

    /**
     * 保存工单粒度投产的批量工序生产数据
     *
     * @param rworkerBatchStepSaveRequestDTO 请求保存参数
     */
    private BatchWorkDetail createSingleWsBatchStep(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        Step step = batchStepSaveBaseInfo.getStep();
        WsStep currWsStep = batchStepSaveBaseInfo.getWsStep();
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(step.getId(), workSheet.getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            throw new ResponseException("error.stepIsFinished", "工序已完成");
        }
        //获取前置工序总共待流转数量，需要区分在线调整工序和普通工序
        //前置工序待流转数量
        int preStepTransferNumber;
        //前置工序合格数量
        int preStepQualifiedNumber = this.preStepTransferNumber(batchStepSaveBaseInfo, rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), Boolean.FALSE);
        //是否为在线调整类型工序
        boolean onlineReworkStep = currWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || currWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName();
        if (onlineReworkStep) {
            preStepTransferNumber = batchProcessRequestServices[0].findOnlineReworkStepTransferNumber(workSheet.getId(), step.getId(), Boolean.FALSE, batchStepSaveBaseInfo);
        } else {
            preStepTransferNumber = preStepQualifiedNumber;
        }
        batchStepSaveBaseInfo.setRealPreStepTransferNumber(preStepQualifiedNumber);
        preStepTransferNumber = (int) Math.ceil(NumberUtils.multiply(preStepTransferNumber, batchStepSaveBaseInfo.getWsStep().getInputRate()).doubleValue());
        //验证请求保存参数
        BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).validateInputNumber(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, batchWorkDetail, preStepTransferNumber);
        if (null == workSheet.getActualStartDate()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
        }
        //保存工单工序详情数据
        BatchWorkDetail saveBatchWorkDetail = BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).saveBatchWorkDetail(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, batchWorkDetail, preStepTransferNumber, preStepQualifiedNumber, onlineReworkStep);
        //是否需要进行抽检,终检
        boolean inspectTask = qualityServices[0].createBatchInspectTask(Boolean.FALSE,null,workSheet,saveBatchWorkDetail.getStep(),saveBatchWorkDetail.getQualifiedNumber());
        batchStepSaveBaseInfo.setInspectTask(inspectTask);
        //保存可能需要复检的不良数据
        boolean stepReinspect = StringUtils.isNotBlank(batchStepSaveBaseInfo.getStepReinspectSerialNumber()) ? qualityServices[0].saveBatchProcessReinspectInfo(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, saveBatchWorkDetail) : Boolean.FALSE;
        //没有复检时保存可能存在的维修分析数据
        if (!stepReinspect) {
            stepReinspect = qualityServices[0].saveBatchProcessMaintainInfo(rworkerBatchStepSaveRequestDTO, batchStepSaveBaseInfo, saveBatchWorkDetail);
        }
        if (stepReinspect && !batchStepSaveBaseInfo.getInspectTask()) {
            batchStepSaveBaseInfo.setInspectTask(stepReinspect);
        }
        if (!batchStepSaveBaseInfo.getInspectTask()) {
            //更新工作台下个可能待做的工序信息
            nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(workSheet.getId(), workSheet, null, batchStepSaveBaseInfo.getWsStepList(), saveBatchWorkDetail, Boolean.FALSE);
        }
        //最后一个工序完成后需要更新工单及销售订单相关数据
        if (StringUtils.isBlank(currWsStep.getAfterStepId()) && saveBatchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).calculateBatchAfterAllStepFinished(batchStepSaveBaseInfo, saveBatchWorkDetail.getTransferNumber());
        }
        //最后一个工序完成
        if (StringUtils.isBlank(currWsStep.getAfterStepId())) {
            saveProductionPlanAndWorkSheetStatistics(rworkerBatchStepSaveRequestDTO, workSheet, saveBatchWorkDetail);
        }
        return saveBatchWorkDetail;
    }

    /**
     * 保存生成线粒度生产计划信息和工单统计
     *
     * @param rworkerBatchStepSaveRequestDTO 保存批量工序参
     * @param workSheet                      工单
     * @param saveBatchWorkDetail            批量工序生产详情
     */
    private void saveProductionPlanAndWorkSheetStatistics(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, WorkSheet workSheet, BatchWorkDetail saveBatchWorkDetail) {
        // 生产计划表 生产线粒度
        Long pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
        Long workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)) {
            productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, saveBatchWorkDetail.getQualifiedNumber(), OperationEnum.ADD);
        }
        // 更新工单统计
        if (Objects.nonNull(workSheet)) {
            workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), rworkerBatchStepSaveRequestDTO.getQualifiedNumber(), rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber(), OperationEnum.ADD);
        }
    }

    /**
     * 保存子工单(工单)批量工序生产详情数据
     *
     * @param rworkerBatchStepSaveRequestDTO 保存工序生产数据参数DTO
     * @param batchWorkDetail                待保存工序详情记录
     * @param preStepTransferNumber          前置工序待流转数
     * @param preStepQualifiedNumber         前置工序合格数
     * @param batchStepSaveBaseInfo          公共区通用参数
     * @param onlineReworkStep               是否为在线调整工序
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 批量工序生产详情
     */
    @Override
    public BatchWorkDetail saveBatchWorkDetail(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO,
                                               RworkerStepProcessBaseDTO batchStepSaveBaseInfo, BatchWorkDetail batchWorkDetail, int preStepTransferNumber, int preStepQualifiedNumber, boolean onlineReworkStep) {

        if (!CollectionUtils.isEmpty(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList()) && rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getNumber).mapToInt(Integer::intValue).sum() != rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber()) {
            throw new ResponseException("error.stepUnqualifiedNumberNotEqualUnqualifiedItemNumber", "工单【" + (Objects.nonNull(batchStepSaveBaseInfo.getSubWorkSheet()) ? batchStepSaveBaseInfo.getSubWorkSheet().getSerialNumber() : batchStepSaveBaseInfo.getWorkSheet().getSerialNumber()) + "】工序不合格总数与不良项目数量不一致");
        }
        batchWorkDetail = Objects.nonNull(batchStepSaveBaseInfo.getSubWorkSheet()) ?
                batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(rworkerBatchStepSaveRequestDTO.getStepId(), batchStepSaveBaseInfo.getSubWorkSheet().getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail()) :
                batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(rworkerBatchStepSaveRequestDTO.getStepId(), batchStepSaveBaseInfo.getWorkSheet().getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            throw new ResponseException("error.stepIsFinished", "工单工序已完成");
        }
        int inputNumber = Objects.nonNull(batchStepSaveBaseInfo.getSubWorkSheet()) ? batchStepSaveBaseInfo.getSubWorkSheet().getNumber() : batchStepSaveBaseInfo.getWorkSheet().getNumber();
        if (batchWorkDetail.getInputNumber() + rworkerBatchStepSaveRequestDTO.getNumber() > inputNumber) {
            throw new ResponseException("error.stepInputNumberOverWorkSheetNumber", "工单工序投产数已超过工单批量");
        }
        //组装批量详情基础数据
        batchWorkDetail.setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + rworkerBatchStepSaveRequestDTO.getQualifiedNumber())
                .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber())
                .setInputNumber(rworkerBatchStepSaveRequestDTO.getNumber() + batchWorkDetail.getInputNumber())
                .setStep(batchStepSaveBaseInfo.getStep())
                .setFinishNumber(batchWorkDetail.getFinishNumber() + rworkerBatchStepSaveRequestDTO.getQualifiedNumber() + rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber())
                .setEffectNumber(batchWorkDetail.getEffectNumber() + rworkerBatchStepSaveRequestDTO.getNumber())
                .setWorkCell(batchStepSaveBaseInfo.getWorkCell())
                .setOperatorId(rworkerBatchStepSaveRequestDTO.getStaffId())
                .setTeamId(rworkerBatchStepSaveRequestDTO.getTeamId())
                .setDeleted(Constants.LONG_ZERO);
        if (Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode())) {
            batchWorkDetail.setSubWorkSheet(batchStepSaveBaseInfo.getSubWorkSheet());
        } else {
            batchWorkDetail.setWorkSheet(batchStepSaveBaseInfo.getWorkSheet());
        }
        //判断当前工序是否已完成
        batchWorkDetail.setFinish(batchWorkDetail.getInputNumber() == preStepTransferNumber ? ConstantsEnum.FINISH_STATUS.getCategoryName() : ConstantsEnum.UNFINISHED_STATUS.getCategoryName());
        if (null == batchWorkDetail.getStartDate()) {
            batchWorkDetail.setStartDate(rworkerBatchStepSaveRequestDTO.getStartTime());
        }
        int originTransferNumber = batchWorkDetail.getTransferNumber();
        //工序若完成则更新流转数量及完成时间
        batchWorkDetail.setTransferNumber(onlineReworkStep ? batchStepSaveBaseInfo.getRealPreStepTransferNumber() + batchWorkDetail.getQualifiedNumber() : preStepQualifiedNumber - batchWorkDetail.getUnqualifiedNumber());
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            batchWorkDetail.setEndDate(LocalDateTime.now());
            batchWorkDetail.setWorkHour(ChronoUnit.SECONDS.between(batchWorkDetail.getStartDate(), batchWorkDetail.getEndDate()));
            batchWorkDetail.setTransferNumber(onlineReworkStep ? batchStepSaveBaseInfo.getRealPreStepTransferNumber() + batchWorkDetail.getQualifiedNumber() : preStepQualifiedNumber - batchWorkDetail.getUnqualifiedNumber());
        } else {
            batchWorkDetail.setTransferNumber(onlineReworkStep ? batchStepSaveBaseInfo.getRealPreStepTransferNumber() + batchWorkDetail.getQualifiedNumber() : batchWorkDetail.getQualifiedNumber());
        }
        //若动态数据参数不为空则保存动态数据
        if (Objects.nonNull(rworkerBatchStepSaveRequestDTO.getStepDynamicDataColumnGetDto())) {
            StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto = rworkerBatchStepSaveRequestDTO.getStepDynamicDataColumnGetDto();
            //补充工序编码和工序名称
            stepDynamicDataColumnGetDto.setStepName(batchWorkDetail.getStep().getName()).setStepCode(batchWorkDetail.getStep().getCode());
            batchWorkDetail.setStepDynamicDataColumnGetDTO(stepDynamicDataColumnGetDto);
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        //更新可能的后续未完成的工序完成状态(单支情况下可能前置工序出现不合格不会继续往下流转)
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            parallelStepProcessServices[0].updateParallelStepBatchDetailInfoWhenBatchStepComplete(batchWorkDetail, batchStepSaveBaseInfo.getWsStep(), batchStepSaveBaseInfo.getWsStepList(), new HashSet<>());
        }
        //更新子工单或者工单的工序完成个数
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            batchWorkDetailService.updateWorkSheetStepCompleteNumberWhenBatchStepComplete(batchStepSaveBaseInfo.getWorkSheet(), batchStepSaveBaseInfo.getSubWorkSheet(), batchStepSaveBaseInfo.getWsStepList(), batchStepSaveBaseInfo.getWsStep(), Boolean.TRUE);
        }
        //更新生产在制看板数据 TODO:需要调整
        workSheetStepStatisticsServices[0].updateWorkSheetStepStatisticsInfo(Objects.nonNull(batchWorkDetail.getSubWorkSheet())
                        ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet(),
                batchWorkDetail.getSubWorkSheet(), batchStepSaveBaseInfo.getStep(), batchStepSaveBaseInfo.getWsStepList(),
                rworkerBatchStepSaveRequestDTO.getNumber(), rworkerBatchStepSaveRequestDTO.getQualifiedNumber(), rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber(), batchWorkDetail.getTransferNumber() - originTransferNumber);
        return batchWorkDetail;
    }

    /**
     * 保存批量详情关联的追溯数据(物料批次、设备不良、易损件等)
     *
     * @param batchWorkDetail                批量详情
     * @param rworkerBatchStepSaveRequestDTO 请求保存工序参数DTO
     * @param rworkerStepProcessBaseDTO      通用基础数据
     */
    public void updateBatchWorkDetailRelationInfo(BatchWorkDetail batchWorkDetail, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //保存批量详情关联的追溯数据(物料批次、设备不良、易损件等)
        this.updateBatchWorkDetailRelationInfoBase(batchWorkDetail, rworkerBatchStepSaveRequestDTO, rworkerStepProcessBaseDTO);
        //保存易损件信息
        wearingPartServices[0].saveBatchWorkDetailWearingPart(batchWorkDetail, rworkerBatchStepSaveRequestDTO);
        //保存员工产量信息
        LocalDate staffPerformWorkDate = commonService.staffWorkRecordDate();
        this.saveBatchStaffPerform(rworkerStepProcessBaseDTO.getSubWsProductionMode(), batchWorkDetail, rworkerBatchStepSaveRequestDTO, staffPerformWorkDate);
        //保存工位宽放时长内下交数据
        BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).wcExtendStepDetailSave(rworkerStepProcessBaseDTO.getSubWsProductionMode(), batchWorkDetail, rworkerBatchStepSaveRequestDTO);
        //保存设备OEE
        facilityServices[0].saveFacilityOee(rworkerBatchStepSaveRequestDTO.getWorkCellId(), rworkerBatchStepSaveRequestDTO.getFacilityIdList(), batchWorkDetail.getFinishNumber(), batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getUnqualifiedNumber());
    }

    /**
     * 保存批量详情关联的追溯数据(物料批次、设备不良等)
     *
     * @param batchWorkDetail                批量详情
     * @param rworkerBatchStepSaveRequestDTO 请求保存工序参数DTO
     * @param rworkerStepProcessBaseDTO      通用基础数据
     */
    public void updateBatchWorkDetailRelationInfoBase(BatchWorkDetail batchWorkDetail, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //保存批量详情物料信息
        materialServices[0].saveBatchWorkDetailMaterialInfo(batchWorkDetail, rworkerBatchStepSaveRequestDTO.getMaterialInfoList(), rworkerBatchStepSaveRequestDTO.getNumber());
        //保存批量详情设备信息
        facilityServices[0].saveBatchWorkDetailFacilityInfo(batchWorkDetail, rworkerBatchStepSaveRequestDTO.getFacilityIdList());
        //保存工序不良项目信息
        BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveBatchWorkDetailUnqualifiedItem(rworkerStepProcessBaseDTO.getSubWsProductionMode(), batchWorkDetail, rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList());
        //保存工序动态数据
        dynamicServices[0].saveBatchWorkDetailStepDynamicDataInfo(batchWorkDetail, rworkerBatchStepSaveRequestDTO.getStepDynamicDataColumnGetDto());
        //保存可能存在的预警信息
        BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveStepProcessWaringInfo(batchWorkDetail, rworkerStepProcessBaseDTO, rworkerBatchStepSaveRequestDTO);
        //更新设备状态为空闲
        facilityServices[0].updateFacilityStatus(rworkerBatchStepSaveRequestDTO.getFacilityIdList(), ConstantsEnum.FACILITY_STATUS_IDLE.getCategoryName());

    }

    /**
     * 保存员工产量及对应不良信息
     *
     * @param subWsProductionMode            true:子工单粒度，false:工单粒度
     * @param batchWorkDetail                批量详情
     * @param rworkerBatchStepSaveRequestDTO 批量工序生产数据参数
     * @param workDate                       记录日期
     */
    public void saveBatchStaffPerform(boolean subWsProductionMode, BatchWorkDetail batchWorkDetail,
                                      RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, LocalDate workDate) {
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform.setBatchWorkDetailId(batchWorkDetail.getId())
                .setStaffId(rworkerBatchStepSaveRequestDTO.getStaffId())
                .setStep(batchWorkDetail.getStep())
                .setSubWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet() : null)
                .setWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet())
                .setWorkCell(new WorkCell(rworkerBatchStepSaveRequestDTO.getWorkCellId()))
                .setRecordDate(workDate)
                .setRecordTime(LocalDateTime.now())
                .setWorkHour(batchWorkDetail.getWorkHour())
                .setInputNumber(rworkerBatchStepSaveRequestDTO.getNumber())
                .setQualifiedNumber(rworkerBatchStepSaveRequestDTO.getQualifiedNumber())
                .setUnqualifiedNumber(rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber()).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);
        //保存员工不良明细信息
        if (rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber() > 0 && ValidateUtils.isValid(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
                staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                        .setRecordTime(staffPerform.getRecordTime())
                        .setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getId()))
                        .setNumber(unqualifiedItemInfo.getNumber()).setDeleted(Constants.LONG_ZERO);
                staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
            });
        }
    }

    /**
     * 保存工位放行时间段内下交数据
     *
     * @param subWsProductionMode            投产模式
     * @param batchWorkDetail                工序批次详情
     * @param rworkerBatchStepSaveRequestDTO 保存工序生产数据参数DTO
     */
    @Override
    public void wcExtendStepDetailSave(boolean subWsProductionMode, BatchWorkDetail batchWorkDetail,
                                       RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {

        //投产工单存在宽放工序
        Optional<WorkCellExtendStepDetail> workCellExtendStepDetailOptional = subWsProductionMode ? workCellExtendStepDetailRepository
                .findTop1BySubWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(batchWorkDetail.getSubWorkSheet().getId(), Boolean.FALSE, Constants.LONG_ZERO) :
                workCellExtendStepDetailRepository.findTop1ByWorkSheetIdAndStatusAndDeletedOrderByRecordTimeDesc(batchWorkDetail.getWorkSheet().getId(), Boolean.FALSE, Constants.LONG_ZERO);

        //投产工位存在宽放记录
        List<WorkCellExtendHistory> workCellExtendHistoryList = workCellExtendHistoryRepository
                .findByWorkCellIdAndResultAndDeleted(rworkerBatchStepSaveRequestDTO.getWorkCellId(), Boolean.FALSE, Constants.LONG_ZERO);

        //投产工单存在宽放工序或者存在工位宽放记录 则记录 宽放工序历史
        if (workCellExtendStepDetailOptional.isPresent() || ValidateUtils.isValid(workCellExtendHistoryList)) {

            WorkCellExtendStepDetail workCellExtendStepDetail = new WorkCellExtendStepDetail();
            workCellExtendStepDetail.setBatchWorkDetail(batchWorkDetail)
                    .setStaffId(rworkerBatchStepSaveRequestDTO.getStaffId())
                    .setStep(batchWorkDetail.getStep())
                    .setSubWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet() : null)
                    .setWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet())
                    .setWorkCell(new WorkCell(rworkerBatchStepSaveRequestDTO.getWorkCellId()))
                    .setRecordTime(LocalDateTime.now())
                    .setInputNumber(rworkerBatchStepSaveRequestDTO.getNumber())
                    .setQualifiedNumber(rworkerBatchStepSaveRequestDTO.getQualifiedNumber())
                    .setUnqualifiedNumber(rworkerBatchStepSaveRequestDTO.getUnqualifiedNumber())
                    .setStatus(Boolean.FALSE).setDeleted(Constants.LONG_ZERO);

            workCellExtendStepDetailOptional.ifPresent(cellExtendStepDetail -> workCellExtendStepDetail.setExtendWorkCell(cellExtendStepDetail.getExtendWorkCell()));

            if (Objects.isNull(workCellExtendStepDetail.getExtendWorkCell()) && ValidateUtils.isValid(workCellExtendHistoryList)) {
                workCellExtendStepDetail.setExtendWorkCell(workCellExtendHistoryList.get(Constants.INT_ZERO).getWorkCell());
            }
            workCellExtendStepDetailRepository.save(workCellExtendStepDetail);
        }
    }

    /**
     * 批量模式下所有工序都完成后进行更新相关统计数据
     *
     * @param rworkerStepProcessBaseDTO 工序保存通用信息
     * @param lastStepQualifiedNumber   最后一个工序的合格数
     */
    @Override
    @Klock(keys = {"#rworkerStepProcessBaseDTO.workSheet.id"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public void calculateBatchAfterAllStepFinished(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, int lastStepQualifiedNumber) {
        WorkSheet workSheet;
        int realFinishedNumber;
        //子工单投产粒度时更新子工单和工单的合格数及不合格数
        if (Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())) {
            SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
            workSheet = subWorkSheet.getWorkSheet();
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - subWorkSheet.getUnqualifiedNumber());
            subWorkSheet.setQualifiedNumber(lastStepQualifiedNumber)
                    .setUnqualifiedNumber(subWorkSheet.getNumber() - lastStepQualifiedNumber);
            if (!rworkerStepProcessBaseDTO.getInspectTask()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                        .setActualEndDate(LocalDateTime.now());
                //删除所有rworker关于该子工单的待做工序数据
                nextTodoStepRepository.deleteBySubWorkSheetId(subWorkSheet.getId());
            }
            subWorkSheetRepository.save(subWorkSheet);
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + subWorkSheet.getUnqualifiedNumber());
            realFinishedNumber = subWorkSheet.getNumber();
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
            //更新在制看板数据
            workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), rworkerStepProcessBaseDTO.getSubWsProductionMode());
        } else {
            //工单投产粒度时更新工单的合格数及不合格数
            workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
            workSheet.setQualifiedNumber(lastStepQualifiedNumber + workSheet.getReworkQualifiedNumber()).setUnqualifiedNumber(workSheet.getNumber() - workSheet.getQualifiedNumber());
            realFinishedNumber = workSheet.getNumber();
            //更新在制看板数据
            workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, null, rworkerStepProcessBaseDTO.getSubWsProductionMode());
        }
        //若非返工单的工单判断是否完成时需要判断是否存在有未完成的返工单在投产
        boolean existReWorkSheetNotFinished = BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).existReworkSheetNotFinished(rworkerStepProcessBaseDTO, workSheet);
        if (!existReWorkSheetNotFinished && !rworkerStepProcessBaseDTO.getInspectTask()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now());
            //删除所有rworker关于该工单的待做工序数据
            nextTodoStepRepository.deleteByWorkSheetId(workSheet.getId());
        }
        workSheetRepository.save(workSheet);
        //工单类型为返工单时需要更新对应正常单返修合格数、合格数及不合格数数据
        BeanUtil.getHighestPrecedenceBean(IBatchProcessSaveService.class).calculateBatchAfterOnlineReworkSheetFinished(workSheet, lastStepQualifiedNumber);
        //正常单需要更新销售订单的完成数量
        iSaleOrderServices[0].calculateBatchSaleOrderAfterAllStepFinished(workSheet, realFinishedNumber);
        //工单完成-更新工单投料单中的倒冲物料库存-以及线边台账
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
            materialServices[0].backFlushMaterialClearInventoryWsFinished(workSheet);
        }
        //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
        if (workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()) {
            rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(workSheet);
        }
    }

    /**
     * 判断是否存在未完成返工单
     *
     * @param rworkerStepProcessBaseDTO 工序保存通用信息
     * @param workSheet                 工单
     * @return boolean 是否存在未完成的返工单
     */
    @Override
    public boolean existReworkSheetNotFinished(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, WorkSheet workSheet) {
        //若非返工单的工单判断是否完成时需要判断是否存在有未完成的返工单在投产
        boolean existReWorkSheetNotFinished = Boolean.FALSE;
        if (rworkerStepProcessBaseDTO.getSubWsProductionMode() && workSheet.getGenerateSubWsStatus() != Constants.INT_TWO) {
            return Boolean.TRUE;
        }
        if (workSheet.getCategory() != ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()) {
            //查询是否存在未处理的维修分析
            Optional<MaintainHistoryDTO> maintainHistoryOptional = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ? rbaseMaintainHistoryProxy.findTop1BySubWorkSheetIdAndStatusAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), MaintainEnum.WAIT_ANALYZE_STATUS.getStatus(), Constants.LONG_ZERO)
                    : rbaseMaintainHistoryProxy.findTop1ByWorkSheetIdAndStatusAndDeleted(workSheet.getId(), MaintainEnum.WAIT_ANALYZE_STATUS.getStatus(), Constants.LONG_ZERO);
            //查询返工单是否都已完成
            List<WsRework> wsReworks = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            existReWorkSheetNotFinished = maintainHistoryOptional.isPresent() || (!CollectionUtils.isEmpty(wsReworks) && wsReworks.stream().anyMatch(wsRework -> wsRework.getReworkWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && wsRework.getReworkWorkSheet().getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()));
        }
        //若非返工单的工单判断是否完成时需要判断是否存在未完成的子工单在投产
        if (!existReWorkSheetNotFinished) {
            List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            existReWorkSheetNotFinished = !CollectionUtils.isEmpty(subWorkSheetList) && subWorkSheetList.stream().anyMatch(subWorkSheet -> subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && subWorkSheet.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
        }
        return existReWorkSheetNotFinished;
    }

    /**
     * 更新返工单对应正常单的合格数、不合格数以及返工合格数，同时根据条件更新工单可能完成的状态
     *
     * @param reworkWorkSheet 返工单
     * @param qualifiedNumber 合格数
     */
    @Override
    public void calculateBatchAfterOnlineReworkSheetFinished(WorkSheet reworkWorkSheet, int qualifiedNumber) {
        if (reworkWorkSheet.getCategory() != ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()) {
            return;
        }
        Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(reworkWorkSheet.getId(), Constants.LONG_ZERO);
        wsReworkOptional.ifPresent(wsRework -> {
            WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
            originWorkSheet.setReworkQualifiedNumber(originWorkSheet.getReworkQualifiedNumber() + qualifiedNumber);
            originWorkSheet.setUnqualifiedNumber(originWorkSheet.getUnqualifiedNumber() - qualifiedNumber);
            originWorkSheet.setQualifiedNumber(originWorkSheet.getQualifiedNumber() + qualifiedNumber);
            if (originWorkSheet.getQualifiedNumber() + originWorkSheet.getUnqualifiedNumber() == originWorkSheet.getNumber()) {
                //若非返工单的工单判断是否完成时需要判断是否存在有未完成的返工单在投产
                boolean allReWorkSheetNotFinished;
                List<WsRework> wsReworks = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(originWorkSheet.getId(), Constants.LONG_ZERO);
                allReWorkSheetNotFinished = !CollectionUtils.isEmpty(wsReworks) && wsReworks.stream().anyMatch(wsReworkTemp -> wsReworkTemp.getReworkWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && wsReworkTemp.getReworkWorkSheet().getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
                if (!allReWorkSheetNotFinished) {
                    originWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now());
                }
            }
            workSheetRepository.save(originWorkSheet);
            //如果是返修单则可能需要更新降级数量（降级时机为下单降级）
            if (originWorkSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()) {
                rbaseDownGradeProxy.updateDownGradeNumberWhenWorkSheetComplete(originWorkSheet);
            }
        });
    }


    /**
     * 通过工单(子工单)ID、当前生产工序快照以及投产粒度是否为子工单获取前置工序流转数
     *
     * @param workSheetId           工单(子工单)ID
     * @param batchStepSaveBaseInfo 当前生产工序快照
     * @param subWsProductionMode   投产粒度是否为子工单
     * @return int
     */
    private int preStepTransferNumber(RworkerStepProcessBaseDTO batchStepSaveBaseInfo, long workSheetId, boolean subWsProductionMode) {
        SubWorkSheet subWorkSheet = null;
        WorkSheet workSheet = null;
        if (subWsProductionMode) {
            subWorkSheet = subWorkSheetRepository.getReferenceById(workSheetId);
            workSheet = subWorkSheet.getWorkSheet();
        } else {
            workSheet = workSheetRepository.getReferenceById(workSheetId);
        }
        WsStep currWsStep = batchStepSaveBaseInfo.getWsStep();
        List<WsStep> wsStepList = batchStepSaveBaseInfo.getWsStepList();
        int productWorkSheetNumber = subWsProductionMode ? subWorkSheet.getNumber() : workSheet.getNumber();
        List<BatchWorkDetail> batchWorkDetailList = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : batchWorkDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (StringUtils.isNotBlank(currWsStep.getPreStepId()) && CollectionUtils.isEmpty(batchWorkDetailList)) {
            throw new ResponseException("error.preStepNotFinished", "前置工序未完成");
        }
        List<BatchWorkDetail> preBatchWorkDetailList = new ArrayList<>();
        if (StringUtils.isNotBlank(currWsStep.getPreStepId())) {
            preBatchWorkDetailList = batchWorkDetailList.stream().filter(batchWorkDetail -> currWsStep.getPreStepId().contains(batchWorkDetail.getStep().getId().toString())).toList();
            if (CollectionUtils.isEmpty(preBatchWorkDetailList) || preBatchWorkDetailList.stream().allMatch(batchWorkDetail -> batchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName())) {
                throw new ResponseException("error.preStepNotFinished", "前置工序未完成");
            }
        }
        //如果是起始工序且没有任何一个起始工序生产了则按照计算起始工序的投产数进行返回
        if (CollectionUtils.isEmpty(batchWorkDetailList)) {
            return (int) Math.ceil(NumberUtils.multiply(productWorkSheetNumber, currWsStep.getInputRate()).doubleValue());
        }
        //默认按照当前待做工序的流转方式去计算当前工序的投产数
        int transferMode = currWsStep.getTransferType();
        //如果存在多个前置工序则按照前置工序的流转方式进行计算当前工序的投产数
        if (StringUtils.isNotBlank(currWsStep.getPreStepId()) && currWsStep.getPreStepId().split(Constants.STR_COMMA).length > Constants.INT_ONE) {
            WsStep preWsStep = wsStepList.stream().filter(wsStep -> currWsStep.getPreStepId().contains(wsStep.getStep().getId().toString())).findFirst().get();
            transferMode = preWsStep.getTransferType();
        }
        //如果前置工序存在独立流转的工序，取前置工序里流转数最小的作为当前工序的投产数
        if (transferMode == TransferModeEnum.CONTINUOUS.getValue()) {
            return parallelStepProcessServices[0].calculateBatchContinuousTransferNumber(wsStepList, currWsStep, batchWorkDetailList, productWorkSheetNumber);
        }
        //如果前置工序存在分拆流转的工序，取前置工序里流转数之和作为当前工序的投产数
        if (transferMode == TransferModeEnum.INDEPENDENT.getValue()) {
            return parallelStepProcessServices[0].calculateBatchIndependentTransferNumber(wsStepList, currWsStep, batchWorkDetailList, productWorkSheetNumber);
        }
        //如果前置工序存在连续流转的工序，取前置工序里最后一个完成工序的流转数之和作为当前工序的投产数
        if (transferMode == TransferModeEnum.SPLIT.getValue()) {
            return parallelStepProcessServices[0].calculateBatchSplitTransferNumber(wsStepList, currWsStep, batchWorkDetailList, productWorkSheetNumber);
        }
        //前置多个工序则默认取流转数最小的
        return preBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt();
    }

    /**
     * 工单请求时需要解绑所有容器
     *
     * @param batchStepSaveBaseInfo 通用保存基础信息
     */
    public void unBindContainerWhenWsRequest(RworkerStepProcessBaseDTO batchStepSaveBaseInfo, List<WsStep> parentWsStepList) {
        if (CollectionUtils.isEmpty(parentWsStepList)) {
            return;
        }
        List<Long> perStepIds = parentWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
        List<ContainerDetail> containerDetails = Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ?
                containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndStatusAndDeleted(batchStepSaveBaseInfo.getSubWorkSheet().getId(), ConstantsEnum.BINDING.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO)
                : containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndStatusAndDeleted(batchStepSaveBaseInfo.getWorkSheet().getId(), ConstantsEnum.BINDING.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetails)) {
            List<ContainerDetail> preContainerDetailList = containerDetails.stream().filter(containerDetail -> perStepIds.contains(containerDetail.getBatchWorkDetail().getStep().getId())).collect(Collectors.toList());
            if (ValidateUtils.isValid(preContainerDetailList)) {
                containerDetailRepository.unbindAllContainerByContainerDetailIds(preContainerDetailList.stream().map(ContainerDetail::getId).collect(Collectors.toList()), LocalDateTime.now());
            }
        }
    }

    /**
     * 预留工序保存可扩展接口
     *
     * @param rworkerBatchStepSaveRequestDTO 待保存工序参数DTO
     */
    @Override
    public void createExtendInfo(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        IBatchProcessSaveService.super.createExtendInfo(rworkerBatchStepSaveRequestDTO);
    }
}
